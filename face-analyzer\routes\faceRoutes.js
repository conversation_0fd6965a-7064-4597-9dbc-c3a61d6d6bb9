const express = require('express');
const router = express.Router();
const {
  uploadImage,
  startAnalysis,
  getAnalysisResult,
  analyzeFace,
  createTestTask,
  debugCompleteFlow,
  analyzeFaceDetectionIssues,
  testSkinConditionImage
} = require('../controllers/faceController');

router.post('/upload', uploadImage);           // Step 1: Upload image, returns fileId
router.post('/start-analysis', startAnalysis); // Step 2: Start analysis with fileId, returns taskId
router.get('/result/:taskId', getAnalysisResult); // Step 3: Get result with taskId



router.post('/analyze', analyzeFace);

module.exports = router;
