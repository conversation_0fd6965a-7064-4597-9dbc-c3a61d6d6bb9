const https = require('https');
const PollingStrategy = require('../utils/pollingStrategy');
const crypto = require('crypto');

let cachedAccessToken = null;
let tokenExpiry = null;

// Function to generate id_token using RSA encryption
function generateIdToken(clientId, clientSecret) {
  const timestamp = new Date().getTime();
  const dataToEncrypt = `client_id=${clientId}&timestamp=${timestamp}`;

  // Format the public key properly
  const pemKey = `-----BEGIN PUBLIC KEY-----\n${clientSecret.match(/.{1,64}/g).join('\n')}\n-----END PUBLIC KEY-----`;

  try {
    // Use explicit RSA padding for compatibility
    const encryptOptions = {
      key: pemKey,
      padding: crypto.constants.RSA_PKCS1_PADDING
    };

    const encrypted = crypto.publicEncrypt(encryptOptions, Buffer.from(dataToEncrypt));
    const base64Result = encrypted.toString('base64');
    return base64Result;
  } catch (error) {
    console.error('Error generating id_token:', error);
    throw new Error('Failed to generate id_token');
  }
}

// Function to authenticate and get access token with timeout
async function getAccessToken() {
  // Check if we have a valid cached token (valid for 2 hours, refresh 10 minutes early)
  if (cachedAccessToken && tokenExpiry && Date.now() < tokenExpiry - 10 * 60 * 1000) {
    return cachedAccessToken;
  }

  const clientId = process.env.PERFECT_CORP_API_KEY;
  const clientSecret = process.env.YOUCAM_API_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error('Missing API credentials');
  }

  console.log('Authenticating with YouCam API...');

  const idToken = generateIdToken(clientId, clientSecret);

  const authPayload = {
    client_id: clientId,
    id_token: idToken
  };

  const options = {
    method: 'POST',
    hostname: 'yce-api-01.perfectcorp.com',
    port: null,
    path: '/s2s/v1.0/client/auth',
    headers: {
      'content-type': 'application/json'
    },
    timeout: 30000 // 30 second timeout for auth
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, function (res) {
      const chunks = [];

      res.on('data', function (chunk) {
        chunks.push(chunk);
      });

      res.on('end', function () {
        const body = Buffer.concat(chunks);
        const responseText = body.toString();

        console.log('Auth response status:', res.statusCode);

        try {
          const responseData = JSON.parse(responseText);

          if (res.statusCode >= 200 && res.statusCode < 300) {
            cachedAccessToken = responseData.result.access_token;
            tokenExpiry = Date.now() + 2 * 60 * 60 * 1000; // 2 hours from now
            resolve(cachedAccessToken);
          } else {
            reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseData.error || responseText}`));
          }
        } catch (parseError) {
          reject(new Error(`Authentication failed: HTTP ${res.statusCode}: ${responseText}`));
        }
      });
    });

    req.on('error', function (error) {
      console.error('Auth request error:', error);
      reject(new Error(`Authentication request failed: ${error.message}`));
    });

    req.on('timeout', function() {
      req.destroy();
      reject(new Error('Authentication request timed out'));
    });

    req.write(JSON.stringify(authPayload));
    req.end();
  });
}

// Optimized image upload with better error handling
exports.uploadImage = async (imageBase64) => {
  try {
    console.log('Uploading image to YouCam...');
    console.log('API Key present:', !!process.env.PERFECT_CORP_API_KEY);
    console.log('API Secret present:', !!process.env.YOUCAM_API_SECRET);

    // Get access token first
    const accessToken = await getAccessToken();
    console.log('Access token obtained successfully');

    // Validate base64 input
    if (!imageBase64 || typeof imageBase64 !== 'string') {
      throw new Error('Invalid base64 image data provided');
    }

    // Convert base64 to buffer to calculate file size
    const imageBuffer = Buffer.from(imageBase64, 'base64');
    const fileSize = imageBuffer.length;

    // Check file size limits (YouCam typically has size limits)
    const maxFileSize = 10 * 1024 * 1024; // 10MB limit
    if (fileSize > maxFileSize) {
      throw new Error(`Image too large: ${fileSize} bytes. Maximum allowed: ${maxFileSize} bytes`);
    }

    // Detect image type and file extension with better detection
    let contentType = 'image/jpeg';
    let fileName = 'image.jpg';

    // Check magic bytes for better image type detection
    const firstBytes = imageBuffer.toString('hex', 0, 8).toLowerCase();
    if (firstBytes.startsWith('89504e47')) { // PNG magic bytes
      contentType = 'image/png';
      fileName = 'image.png';
    } else if (firstBytes.startsWith('47494638')) { // GIF magic bytes
      contentType = 'image/gif';
      fileName = 'image.gif';
    } else if (firstBytes.startsWith('ffd8ff')) { // JPEG magic bytes
      contentType = 'image/jpeg';
      fileName = 'image.jpg';
    }

    console.log('Detected image type:', contentType);
    console.log('File size:', fileSize, 'bytes');

    // Prepare the payload according to API documentation
    const payload = {
      files: [{
        content_type: contentType,
        file_name: fileName,
        file_size: fileSize
      }]
    };

    console.log('Sending payload:', JSON.stringify(payload, null, 2));

    const options = {
      method: 'POST',
      hostname: 'yce-api-01.perfectcorp.com',
      port: null,
      path: '/s2s/v1.1/file/face-attr-analysis',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'content-type': 'application/json'
      },
      timeout: 60000 // 60 second timeout for upload
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, function (res) {
        const chunks = [];

        res.on('data', function (chunk) {
          chunks.push(chunk);
        });

        res.on('end', function () {
          const body = Buffer.concat(chunks);
          const responseText = body.toString();

          console.log('Upload response status:', res.statusCode);
          console.log('Upload response headers:', res.headers);
          console.log('Upload response body:', responseText);

          try {
            const responseData = JSON.parse(responseText);

            if (res.statusCode >= 200 && res.statusCode < 300) {
              // Extract file_id from the response
              if (responseData.result && responseData.result.files && responseData.result.files.length > 0) {
                const fileId = responseData.result.files[0].file_id;
                console.log('File uploaded successfully, file_id:', fileId);
                resolve(fileId);
              } else {
                console.error('Unexpected response format:', responseData);
                reject(new Error('File upload succeeded but no file_id found in response'));
              }
            } else {
              console.error('API Error Response:', responseData);
              reject(new Error(`HTTP ${res.statusCode}: ${responseData.message || responseData.error || responseText}`));
            }
          } catch (parseError) {
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(responseText);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
            }
          }
        });
      });

      req.on('error', function (error) {
        console.error('Request error:', error);
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.on('timeout', function() {
        req.destroy();
        reject(new Error('Upload request timed out'));
      });

      req.write(JSON.stringify(payload));
      req.end();
    });

  } catch (error) {
    console.error('Upload error details:', error);
    throw new Error(`Image upload failed: ${error.message}`);
  }
};

// Optimized analysis start with simpler feature set
exports.startAnalysis = async (fileId) => {
  try {
    console.log('Starting analysis for file ID:', fileId);

    // Get access token
    const accessToken = await getAccessToken();

    // Simplified payload for faster processing - start with basic features only
    const payload = {
      request_id: 0,
      payload: {
        file_sets: {
          src_ids: [fileId]
        },
        actions: [
          {
            id: 0,
            params: {
              face_angle_strictness_level: "low",
              // Start with just one feature for faster processing
              features: [
                "faceShape"  // Most reliable feature, process others separately if needed
              ]
            }
          }
        ]
      }
    };

    console.log('Analysis payload:', JSON.stringify(payload, null, 2));
    console.log('Using simplified feature set for faster processing');

    const options = {
      method: 'POST',
      hostname: 'yce-api-01.perfectcorp.com',
      port: null,
      path: '/s2s/v1.0/task/face-attr-analysis',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'content-type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, function (res) {
        const chunks = [];

        res.on('data', function (chunk) {
          chunks.push(chunk);
        });

        res.on('end', function () {
          const body = Buffer.concat(chunks);
          const responseText = body.toString();

          console.log('Analysis start response:', responseText);

          try {
            const responseData = JSON.parse(responseText);

            if (res.statusCode >= 200 && res.statusCode < 300) {
              // Extract task_id from the response
              if (responseData.result && responseData.result.task_id) {
                const taskId = responseData.result.task_id;
                console.log('Analysis started with task ID:', taskId);
                resolve(taskId);
              } else {
                console.error('Unexpected response format:', responseData);
                reject(new Error('Analysis started but no task_id found in response'));
              }
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${responseData.message || responseData.error || responseText}`));
            }
          } catch (parseError) {
            reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
          }
        });
      });

      req.on('error', function (error) {
        console.error('Request error:', error);
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.on('timeout', function() {
        req.destroy();
        reject(new Error('Analysis start request timed out'));
      });

      req.write(JSON.stringify(payload));
      req.end();
    });

  } catch (error) {
    console.error('Analysis start error:', error);
    throw new Error(`Analysis start failed: ${error.message}`);
  }
};

// Export getAccessToken for testing purposes
exports.getAccessToken = getAccessToken;

// Completely rewritten getAnalysisResult with better timeout handling
exports.getAnalysisResult = async (taskId, options = {}) => {
  try {
    console.log('Getting analysis result for task ID:', taskId);

    // More conservative and realistic polling configuration
    const pollingConfig = {
      initialInterval: options.initialInterval || 2000,      // Start with 2 seconds - API needs time
      maxInterval: options.maxInterval || 8000,              // Max 8 seconds between polls
      maxTotalTime: options.maxTotalTime || 180000,          // 3 minutes total timeout (more realistic)
      maxAttempts: options.maxAttempts || 90,                // Max 90 attempts
      fastPollingDuration: options.fastPollingDuration || 30000, // Fast polling for first 30 seconds only
      fastPollingInterval: options.fastPollingInterval || 2000   // 2 seconds during fast polling
    };

    // Circuit breaker pattern - fail fast if too many consecutive failures
    let consecutiveFailures = 0;
    const maxConsecutiveFailures = 5;

    console.log('Using optimized polling configuration:', pollingConfig);

    // Initialize polling strategy
    const pollingStrategy = new PollingStrategy(pollingConfig);

    // Get access token once for all polling attempts
    const accessToken = await getAccessToken();

    // Keep track of last known status to detect stuck states
    let lastKnownStatus = null;
    let statusStuckCount = 0;
    const maxStatusStuckCount = 10; // If status doesn't change for 10 polls, fail

    // Define the polling function with better error handling
    const pollFunction = async () => {
      const options = {
        method: 'GET',
        hostname: 'yce-api-01.perfectcorp.com',
        port: null,
        path: `/s2s/v1.0/task/face-attr-analysis?task_id=${encodeURIComponent(taskId)}`,
        headers: { 
          Authorization: `Bearer ${accessToken}`,
          'User-Agent': 'YouCam-API-Client/1.0'
        },
        timeout: 15000 // 15 second timeout per request
      };

      const result = await new Promise((resolve, reject) => {
        const req = https.request(options, function (res) {
          const chunks = [];

          res.on('data', function (chunk) {
            chunks.push(chunk);
          });

          res.on('end', function () {
            const body = Buffer.concat(chunks);
            const responseText = body.toString();

            try {
              const responseData = JSON.parse(responseText);

              if (res.statusCode >= 200 && res.statusCode < 300) {
                consecutiveFailures = 0; // Reset failure counter
                resolve(responseData);
              } else {
                consecutiveFailures++;
                if (consecutiveFailures >= maxConsecutiveFailures) {
                  reject(new Error(`Too many consecutive failures (${consecutiveFailures}). HTTP ${res.statusCode}: ${responseData.message || responseData.error || responseText}`));
                } else {
                  reject(new Error(`HTTP ${res.statusCode}: ${responseData.message || responseData.error || responseText}`));
                }
              }
            } catch (parseError) {
              consecutiveFailures++;
              reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
            }
          });
        });

        req.on('error', function (error) {
          console.error('Request error:', error);
          consecutiveFailures++;
          reject(new Error(`Request failed: ${error.message}`));
        });

        req.on('timeout', function() {
          req.destroy();
          consecutiveFailures++;
          reject(new Error('Polling request timed out'));
        });

        req.end();
      });

      // Extract the actual task status from the API response
      const taskStatus = result.result?.status;
      const analysisResult = result.result?.results;

      // Check for stuck status
      if (taskStatus === lastKnownStatus) {
        statusStuckCount++;
        if (statusStuckCount >= maxStatusStuckCount) {
          throw new Error(`Analysis appears stuck in '${taskStatus}' status for too long. This may indicate a server-side issue.`);
        }
      } else {
        statusStuckCount = 0;
        lastKnownStatus = taskStatus;
      }

      // Success case
      if (taskStatus === 'success') {
        console.log('✓ Analysis completed successfully');
        console.log('Analysis results:', JSON.stringify(analysisResult, null, 2));
        return { success: true, data: analysisResult };
      }

      // Failure cases - fail fast
      if (taskStatus === 'error' || taskStatus === 'failed') {
        const errorMessage = result.result?.error_message || 'Analysis failed';
        const errorCode = result.result?.error || 'unknown_error';
        console.error('Analysis failed with error:', errorCode, errorMessage);
        throw new Error(`Analysis failed: ${errorMessage} (${errorCode})`);
      }

      // Check for specific face detection issues
      if (result.result?.error_message) {
        const errorMsg = result.result.error_message.toLowerCase();
        if (errorMsg.includes('face') || errorMsg.includes('detection') || errorMsg.includes('not found')) {
          throw new Error(`Face detection failed: ${result.result.error_message}`);
        }
      }

      // Return status for continued polling
      return {
        success: false,
        status: taskStatus || 'running',
        message: result.result?.message || 'Analysis in progress',
        rawResponse: result
      };
    };

    // Progress callback with better logging
    const onProgress = (progressInfo, lastResponse) => {
      // Log every 5 attempts to reduce noise
      if (progressInfo.attempt % 5 === 0) {
        const elapsed = Math.round(progressInfo.elapsedSeconds);
        const remaining = Math.round(progressInfo.remainingSeconds);
        console.log(`⏳ Polling attempt ${progressInfo.attempt}/${pollingConfig.maxAttempts} (${elapsed}s elapsed, ~${remaining}s remaining)`);
        console.log(`   Status: ${lastResponse?.status || 'unknown'} | Phase: ${progressInfo.phase} | Progress: ${progressInfo.progressPercent}%`);
      }
    };

    const onAttempt = (attemptNumber, elapsedTime) => {
      if (attemptNumber === 1) {
        console.log('🚀 Starting optimized polling strategy...');
      }
    };

    // Execute polling with enhanced strategy
    const result = await pollingStrategy.poll(pollFunction, {
      onProgress,
      onAttempt
    });

    return result.data;

  } catch (error) {
    console.error('❌ Analysis result error:', error);

    // Provide more helpful error messages
    if (error.message.includes('timeout after')) {
      const suggestion = `
Analysis timed out. This can happen due to:
1. Complex image requiring longer processing time
2. High server load at Perfect Corp
3. Network connectivity issues

Suggestions:
- Try with a simpler image (clear face, good lighting)
- Reduce the number of features being analyzed
- Retry during off-peak hours
- Consider implementing exponential backoff with jitter`;
      
      throw new Error(`${error.message}${suggestion}`);
    }

    if (error.message.includes('Face detection failed')) {
      const suggestion = `
Face detection failed. Please ensure:
1. Image contains a clear, visible face
2. Face is not heavily obscured or at extreme angles
3. Image has good lighting and resolution
4. Only one primary face is visible in the image`;
      
      throw new Error(`${error.message}${suggestion}`);
    }

    throw new Error(`Getting analysis result failed: ${error.message}`);
  }
};

// Helper function to retry analysis with different configurations
exports.retryAnalysisWithFallback = async (fileId, maxRetries = 3) => {
  const configurations = [
    // First try: Just face shape (fastest)
    { features: ["faceShape"], description: "face shape only" },
    // Second try: Two basic features
    { features: ["faceShape", "eyeShape"], description: "face and eye shape" },
    // Third try: All features (slowest)
    { features: ["eyeShape", "faceShape", "lipShape"], description: "all features" }
  ];

  for (let i = 0; i < Math.min(maxRetries, configurations.length); i++) {
    try {
      console.log(`🔄 Attempt ${i + 1}: Trying analysis with ${configurations[i].description}`);
      
      // Start analysis with current configuration
      const taskId = await exports.startAnalysisWithConfig(fileId, configurations[i].features);
      
      // Get results with progressively longer timeouts
      const timeoutMultiplier = i + 1;
      const results = await exports.getAnalysisResult(taskId, {
        maxTotalTime: 120000 * timeoutMultiplier, // 2, 4, 6 minutes
        maxAttempts: 60 * timeoutMultiplier
      });
      
      console.log(`✅ Analysis succeeded with ${configurations[i].description}`);
      return results;
      
    } catch (error) {
      console.log(`❌ Attempt ${i + 1} failed: ${error.message}`);
      if (i === maxRetries - 1) {
        throw error; // Re-throw on final attempt
      }
      
      // Wait before next attempt
      await new Promise(resolve => setTimeout(resolve, 5000 * (i + 1)));
    }
  }
};

// Helper function to start analysis with specific features
exports.startAnalysisWithConfig = async (fileId, features) => {
  try {
    console.log('Starting analysis for file ID:', fileId, 'with features:', features);

    const accessToken = await getAccessToken();

    const payload = {
      request_id: 0,
      payload: {
        file_sets: {
          src_ids: [fileId]
        },
        actions: [
          {
            id: 0,
            params: {
              face_angle_strictness_level: "low",
              features: features
            }
          }
        ]
      }
    };

    const options = {
      method: 'POST',
      hostname: 'yce-api-01.perfectcorp.com',
      port: null,
      path: '/s2s/v1.0/task/face-attr-analysis',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'content-type': 'application/json'
      },
      timeout: 30000
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, function (res) {
        const chunks = [];

        res.on('data', function (chunk) {
          chunks.push(chunk);
        });

        res.on('end', function () {
          const body = Buffer.concat(chunks);
          const responseText = body.toString();

          try {
            const responseData = JSON.parse(responseText);

            if (res.statusCode >= 200 && res.statusCode < 300) {
              if (responseData.result && responseData.result.task_id) {
                resolve(responseData.result.task_id);
              } else {
                reject(new Error('Analysis started but no task_id found in response'));
              }
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${responseData.message || responseData.error || responseText}`));
            }
          } catch (parseError) {
            reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
          }
        });
      });

      req.on('error', function (error) {
        reject(new Error(`Request failed: ${error.message}`));
      });

      req.on('timeout', function() {
        req.destroy();
        reject(new Error('Analysis start request timed out'));
      });

      req.write(JSON.stringify(payload));
      req.end();
    });

  } catch (error) {
    throw new Error(`Analysis start failed: ${error.message}`);
  }
};